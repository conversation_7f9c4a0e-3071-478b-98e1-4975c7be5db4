<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;0015aa87-86ec-4492-bcd3-7592331aed69&quot;,&quot;conversations&quot;:{&quot;528d8f7d-e2b4-473f-9d2b-bfa70aa21af5&quot;:{&quot;id&quot;:&quot;528d8f7d-e2b4-473f-9d2b-bfa70aa21af5&quot;,&quot;createdAtIso&quot;:&quot;2025-06-10T07:58:03.407Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-10T07:58:03.407Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;0015aa87-86ec-4492-bcd3-7592331aed69&quot;:{&quot;id&quot;:&quot;0015aa87-86ec-4492-bcd3-7592331aed69&quot;,&quot;createdAtIso&quot;:&quot;2025-06-10T07:58:03.410Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-10T07:58:15.436Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;96061145-54a4-43a7-92a1-909771ce0451&quot;,&quot;uuid&quot;:&quot;3644b445-bd34-4388-9224-5cbb6bc82d29&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1749542283411,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;301822a8-683b-47ef-b727-6b8992be59f3&quot;,&quot;request_message&quot;:&quot;@查询帮我看下这个页面都是什么意思， &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;mention&quot;,&quot;attrs&quot;:{&quot;id&quot;:&quot;/查询&quot;,&quot;label&quot;:&quot;查询&quot;,&quot;data&quot;:{&quot;label&quot;:&quot;查询&quot;,&quot;name&quot;:&quot;查询&quot;,&quot;id&quot;:&quot;/查询&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;查询&quot;},&quot;type&quot;:&quot;item&quot;}}},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;帮我看下这个页面都是什么意思， &quot;}]}]},&quot;mentioned_items&quot;:[{&quot;label&quot;:&quot;查询&quot;,&quot;name&quot;:&quot;查询&quot;,&quot;id&quot;:&quot;/查询&quot;,&quot;file&quot;:{&quot;repoRoot&quot;:&quot;&quot;,&quot;pathName&quot;:&quot;查询&quot;},&quot;type&quot;:&quot;item&quot;}],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended because we believe you may be using multiple free trials. Please contact support [here](https://docs.augmentcode.com/troubleshooting/support) for more information.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;@`查询`帮我看下这个页面都是什么意思， &quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;I:/编程/查询接口&quot;,&quot;folderRoot&quot;:&quot;I:/编程/查询接口&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-10T07:58:15.436Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended because we believe you may be using multiple free trials. Please contact support [here](https://docs.augmentcode.com/troubleshooting/support) for more information.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-9871eeca-8391-4b8f-b500-60ccf0a40270&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;5237f639-39df-433a-bf01-ffdf3e5003f4&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>