# WordPress 用户操作实用示例

## 快速开始

### 基础配置
```javascript
const API_BASE = 'http://*************:3080/index.php/wp-json/wp/v2';
const ADMIN_TOKEN = 'your_admin_token_here'; // 管理员令牌
```

---

## 1. 获取用户信息

### 1.1 获取所有用户列表
```javascript
async function getAllUsers() {
  const response = await fetch(`${API_BASE}/users`);
  const users = await response.json();
  console.log('用户列表:', users);
  return users;
}
```

### 1.2 搜索用户
```javascript
async function searchUsers(keyword) {
  const response = await fetch(`${API_BASE}/users?search=${encodeURIComponent(keyword)}`);
  const users = await response.json();
  console.log('搜索结果:', users);
  return users;
}

// 使用示例
searchUsers('admin'); // 搜索包含"admin"的用户
```

### 1.3 获取特定角色的用户
```javascript
async function getUsersByRole(role) {
  const response = await fetch(`${API_BASE}/users?roles=${role}`);
  const users = await response.json();
  console.log(`${role}角色用户:`, users);
  return users;
}

// 使用示例
getUsersByRole('editor'); // 获取所有编辑用户
getUsersByRole('author'); // 获取所有作者用户
```

### 1.4 获取单个用户详细信息
```javascript
async function getUserById(userId) {
  const response = await fetch(`${API_BASE}/users/${userId}`);
  const user = await response.json();
  console.log('用户详情:', user);
  return user;
}

// 获取当前登录用户信息
async function getCurrentUser(userToken) {
  const response = await fetch(`${API_BASE}/users/me`, {
    headers: {
      'Authorization': `Bearer ${userToken}`
    }
  });
  const user = await response.json();
  console.log('当前用户:', user);
  return user;
}
```

---

## 2. 创建用户

### 2.1 创建基本用户
```javascript
async function createBasicUser(userData) {
  try {
    const response = await fetch(`${API_BASE}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${ADMIN_TOKEN}`
      },
      body: JSON.stringify({
        username: userData.username,
        email: userData.email,
        password: userData.password,
        name: userData.name,
        roles: ['subscriber'] // 默认为订阅者
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }

    const newUser = await response.json();
    console.log('用户创建成功:', newUser);
    return newUser;
  } catch (error) {
    console.error('创建用户失败:', error.message);
    throw error;
  }
}

// 使用示例
const newUserData = {
  username: 'testuser123',
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: '测试用户'
};

createBasicUser(newUserData);
```

### 2.2 创建不同角色的用户
```javascript
// 创建编辑用户
async function createEditor(userData) {
  return await createUserWithRole(userData, 'editor');
}

// 创建作者用户
async function createAuthor(userData) {
  return await createUserWithRole(userData, 'author');
}

// 创建贡献者用户
async function createContributor(userData) {
  return await createUserWithRole(userData, 'contributor');
}

// 通用创建用户函数
async function createUserWithRole(userData, role) {
  const response = await fetch(`${API_BASE}/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${ADMIN_TOKEN}`
    },
    body: JSON.stringify({
      username: userData.username,
      email: userData.email,
      password: userData.password,
      name: userData.name,
      first_name: userData.firstName,
      last_name: userData.lastName,
      roles: [role]
    })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message);
  }

  return await response.json();
}

// 使用示例
const editorData = {
  username: 'editor001',
  email: '<EMAIL>',
  password: 'EditorPass123!',
  name: '编辑用户',
  firstName: '编辑',
  lastName: '用户'
};

createEditor(editorData);
```

### 2.3 批量创建用户
```javascript
async function createMultipleUsers(usersData) {
  const results = [];
  
  for (const userData of usersData) {
    try {
      const user = await createBasicUser(userData);
      results.push({ success: true, user });
      
      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      results.push({ success: false, error: error.message, userData });
    }
  }
  
  console.log('批量创建结果:', results);
  return results;
}

// 使用示例
const multipleUsers = [
  {
    username: 'user001',
    email: '<EMAIL>',
    password: 'Password123!',
    name: '用户001'
  },
  {
    username: 'user002',
    email: '<EMAIL>',
    password: 'Password123!',
    name: '用户002'
  }
];

createMultipleUsers(multipleUsers);
```

---

## 3. 更新用户信息

### 3.1 更新基本信息
```javascript
async function updateUserBasicInfo(userId, updateData, token) {
  try {
    const response = await fetch(`${API_BASE}/users/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }

    const updatedUser = await response.json();
    console.log('用户更新成功:', updatedUser);
    return updatedUser;
  } catch (error) {
    console.error('更新用户失败:', error.message);
    throw error;
  }
}

// 使用示例 - 更新用户个人信息
const updateData = {
  name: '新的显示名称',
  first_name: '新名字',
  last_name: '新姓氏',
  email: '<EMAIL>',
  url: 'https://newwebsite.com',
  description: '更新后的个人简介'
};

updateUserBasicInfo(123, updateData, 'user_token_or_admin_token');
```

### 3.2 更改用户密码
```javascript
async function changeUserPassword(userId, newPassword, token) {
  return await updateUserBasicInfo(userId, { password: newPassword }, token);
}

// 使用示例
changeUserPassword(123, 'NewSecurePassword456!', 'user_token');
```

### 3.3 更改用户角色 (仅管理员)
```javascript
async function changeUserRole(userId, newRole) {
  return await updateUserBasicInfo(userId, { roles: [newRole] }, ADMIN_TOKEN);
}

// 使用示例
changeUserRole(123, 'editor'); // 将用户提升为编辑
changeUserRole(123, 'subscriber'); // 将用户降级为订阅者
```

### 3.4 批量更新用户
```javascript
async function updateMultipleUsers(updates) {
  const results = [];
  
  for (const update of updates) {
    try {
      const user = await updateUserBasicInfo(update.userId, update.data, update.token || ADMIN_TOKEN);
      results.push({ success: true, userId: update.userId, user });
      
      // 添加延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      results.push({ success: false, userId: update.userId, error: error.message });
    }
  }
  
  console.log('批量更新结果:', results);
  return results;
}

// 使用示例
const updates = [
  {
    userId: 123,
    data: { name: '更新的用户123' }
  },
  {
    userId: 124,
    data: { roles: ['author'] }
  }
];

updateMultipleUsers(updates);
```

---

## 4. 删除用户

### 4.1 删除用户 (重新分配文章)
```javascript
async function deleteUser(userId, reassignToUserId = null) {
  try {
    let url = `${API_BASE}/users/${userId}?force=true`;
    if (reassignToUserId) {
      url += `&reassign=${reassignToUserId}`;
    }

    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`
      }
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message);
    }

    const result = await response.json();
    console.log('用户删除成功:', result);
    return result;
  } catch (error) {
    console.error('删除用户失败:', error.message);
    throw error;
  }
}

// 使用示例
deleteUser(123, 1); // 删除用户123，将其文章重新分配给用户1
deleteUser(124); // 删除用户124，同时删除其所有文章
```

---

## 5. 实用工具函数

### 5.1 用户验证
```javascript
async function validateUser(username, email) {
  try {
    // 检查用户名是否存在
    const usernameCheck = await fetch(`${API_BASE}/users?search=${username}`);
    const usernameResults = await usernameCheck.json();
    
    if (usernameResults.some(user => user.slug === username)) {
      return { valid: false, message: '用户名已存在' };
    }

    // 检查邮箱是否存在
    const emailCheck = await fetch(`${API_BASE}/users?search=${email}`);
    const emailResults = await emailCheck.json();
    
    if (emailResults.some(user => user.email === email)) {
      return { valid: false, message: '邮箱已存在' };
    }

    return { valid: true, message: '用户名和邮箱可用' };
  } catch (error) {
    return { valid: false, message: '验证失败' };
  }
}

// 使用示例
const validation = await validateUser('newuser', '<EMAIL>');
if (validation.valid) {
  console.log('可以创建用户');
} else {
  console.log('验证失败:', validation.message);
}
```

### 5.2 密码强度验证
```javascript
function validatePassword(password) {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const errors = [];
  
  if (password.length < minLength) {
    errors.push(`密码长度至少${minLength}位`);
  }
  if (!hasUpperCase) {
    errors.push('密码必须包含大写字母');
  }
  if (!hasLowerCase) {
    errors.push('密码必须包含小写字母');
  }
  if (!hasNumbers) {
    errors.push('密码必须包含数字');
  }
  if (!hasSpecialChar) {
    errors.push('密码必须包含特殊字符');
  }

  return {
    valid: errors.length === 0,
    errors: errors
  };
}

// 使用示例
const passwordCheck = validatePassword('MyPassword123!');
if (passwordCheck.valid) {
  console.log('密码强度合格');
} else {
  console.log('密码问题:', passwordCheck.errors);
}
```

### 5.3 用户统计
```javascript
async function getUserStatistics() {
  try {
    const allUsers = await fetch(`${API_BASE}/users?per_page=100`);
    const users = await allUsers.json();
    
    const stats = {
      total: users.length,
      byRole: {},
      recentRegistrations: 0
    };

    // 统计各角色用户数量
    users.forEach(user => {
      user.roles.forEach(role => {
        stats.byRole[role] = (stats.byRole[role] || 0) + 1;
      });
    });

    // 统计最近30天注册的用户
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    stats.recentRegistrations = users.filter(user => {
      const registeredDate = new Date(user.registered_date);
      return registeredDate > thirtyDaysAgo;
    }).length;

    console.log('用户统计:', stats);
    return stats;
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return null;
  }
}

// 使用示例
getUserStatistics();
```

---

## 6. 错误处理示例

```javascript
class UserAPIError extends Error {
  constructor(message, code, status) {
    super(message);
    this.code = code;
    this.status = status;
  }
}

async function safeUserOperation(operation) {
  try {
    return await operation();
  } catch (error) {
    if (error.response) {
      const errorData = await error.response.json();
      throw new UserAPIError(
        errorData.message || '操作失败',
        errorData.code,
        error.response.status
      );
    } else {
      throw new UserAPIError('网络错误或服务不可用', 'network_error', 0);
    }
  }
}

// 使用示例
try {
  const user = await safeUserOperation(() => createBasicUser(userData));
  console.log('用户创建成功:', user);
} catch (error) {
  if (error instanceof UserAPIError) {
    console.error(`错误 [${error.code}]: ${error.message}`);
  } else {
    console.error('未知错误:', error);
  }
}
```

这个实用示例文件提供了WordPress用户管理的所有常见操作的完整代码示例，您可以直接复制使用或根据需要进行修改。
