# WordPress REST API 快速参考表

## 基础信息
- **基础URL**: `http://*************:3080/index.php/wp-json/wp/v2`
- **格式**: JSON
- **认证**: Cookie/OAuth/Application Password

---

## 核心接口一览表

| 功能分类 | 接口路径 | GET | POST | PUT/PATCH | DELETE | 说明 |
|---------|----------|-----|------|-----------|--------|------|
| **API发现** | `/wp/v2` | ✅ | ❌ | ❌ | ❌ | 获取API信息 |
| **文章管理** | `/wp/v2/posts` | ✅ | ✅ | ❌ | ❌ | 文章列表/创建 |
| | `/wp/v2/posts/{id}` | ✅ | ✅ | ✅ | ✅ | 单篇文章操作 |
| **页面管理** | `/wp/v2/pages` | ✅ | ✅ | ❌ | ❌ | 页面列表/创建 |
| | `/wp/v2/pages/{id}` | ✅ | ✅ | ✅ | ✅ | 单个页面操作 |
| **修订版本** | `/wp/v2/posts/{id}/revisions` | ✅ | ❌ | ❌ | ❌ | 修订版本列表 |
| | `/wp/v2/posts/{parent}/revisions/{id}` | ✅ | ❌ | ❌ | ✅ | 单个修订版本 |
| **自动保存** | `/wp/v2/posts/{id}/autosaves` | ✅ | ✅ | ❌ | ❌ | 自动保存管理 |
| | `/wp/v2/posts/{parent}/autosaves/{id}` | ✅ | ❌ | ❌ | ❌ | 单个自动保存 |
| **媒体文件** | `/wp/v2/media` | ✅ | ✅ | ❌ | ❌ | 媒体列表/上传 |
| | `/wp/v2/media/{id}` | ✅ | ✅ | ✅ | ✅ | 单个媒体操作 |
| **用户管理** | `/wp/v2/users` | ✅ | ✅ | ❌ | ❌ | 用户列表/创建 |
| | `/wp/v2/users/{id}` | ✅ | ✅ | ✅ | ✅ | 单个用户操作 |
| **评论管理** | `/wp/v2/comments` | ✅ | ✅ | ❌ | ❌ | 评论列表/创建 |
| | `/wp/v2/comments/{id}` | ✅ | ✅ | ✅ | ✅ | 单个评论操作 |
| **分类管理** | `/wp/v2/categories` | ✅ | ✅ | ❌ | ❌ | 分类列表/创建 |
| | `/wp/v2/categories/{id}` | ✅ | ✅ | ✅ | ✅ | 单个分类操作 |
| **标签管理** | `/wp/v2/tags` | ✅ | ✅ | ❌ | ❌ | 标签列表/创建 |
| | `/wp/v2/tags/{id}` | ✅ | ✅ | ✅ | ✅ | 单个标签操作 |
| **自定义分类** | `/wp/v2/topics` | ✅ | ✅ | ❌ | ❌ | 主题列表/创建 |
| | `/wp/v2/topics/{id}` | ✅ | ✅ | ✅ | ✅ | 单个主题操作 |

---

## 常用查询参数速查

### 分页参数
```
?page=1&per_page=10&offset=0
```

### 搜索参数
```
?search=关键词&search_columns[]=post_title&search_columns[]=post_content
```

### 筛选参数
```
?author=1&status=publish&categories=5&tags=10
?include[]=1&include[]=2&exclude[]=3
?slug=文章别名
```

### 排序参数
```
?orderby=date&order=desc
?orderby=title&order=asc
?orderby=author&order=desc
```

### 日期筛选
```
?after=2024-01-01T00:00:00&before=2024-12-31T23:59:59
?modified_after=2024-01-01T00:00:00
```

### 高级参数
```
?context=edit          # 获取编辑上下文的字段
?_embed=1              # 嵌入关联数据
?_fields=id,title,date # 只返回指定字段
```

---

## 文章状态值

| 状态值 | 说明 |
|--------|------|
| `publish` | 已发布 |
| `draft` | 草稿 |
| `pending` | 待审核 |
| `private` | 私密 |
| `future` | 定时发布 |
| `trash` | 回收站 |
| `auto-draft` | 自动草稿 |

---

## 文章格式类型

| 格式 | 说明 |
|------|------|
| `standard` | 标准格式 |
| `aside` | 日志 |
| `gallery` | 相册 |
| `link` | 链接 |
| `image` | 图像 |
| `quote` | 引语 |
| `status` | 状态 |
| `video` | 视频 |
| `audio` | 音频 |
| `chat` | 聊天 |

---

## 常用HTTP状态码

| 状态码 | 说明 |
|--------|------|
| `200` | 请求成功 |
| `201` | 创建成功 |
| `400` | 请求错误 |
| `401` | 未授权 |
| `403` | 禁止访问 |
| `404` | 资源未找到 |
| `500` | 服务器错误 |

---

## 快速示例

### 获取最新文章
```bash
curl "http://*************:3080/index.php/wp-json/wp/v2/posts?per_page=5&orderby=date&order=desc"
```

### 搜索文章
```bash
curl "http://*************:3080/index.php/wp-json/wp/v2/posts?search=WordPress"
```

### 获取特定分类文章
```bash
curl "http://*************:3080/index.php/wp-json/wp/v2/posts?categories=1"
```

### 创建文章 (需要认证)
```bash
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/posts" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "新文章标题",
    "content": "文章内容",
    "status": "publish",
    "categories": [1],
    "tags": [2, 3]
  }'
```

### 更新文章
```bash
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/posts/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "更新后的标题",
    "status": "publish"
  }'
```

### 删除文章
```bash
curl -X DELETE "http://*************:3080/index.php/wp-json/wp/v2/posts/123?force=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## JavaScript 示例

### 使用 Fetch API
```javascript
// 获取文章列表
async function getPosts() {
  const response = await fetch('http://*************:3080/index.php/wp-json/wp/v2/posts');
  const posts = await response.json();
  return posts;
}

// 创建文章
async function createPost(postData, token) {
  const response = await fetch('http://*************:3080/index.php/wp-json/wp/v2/posts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(postData)
  });
  return await response.json();
}
```

### 使用 jQuery
```javascript
// 获取文章列表
$.get('http://*************:3080/index.php/wp-json/wp/v2/posts', function(posts) {
  console.log(posts);
});

// 搜索文章
$.get('http://*************:3080/index.php/wp-json/wp/v2/posts?search=关键词', function(posts) {
  console.log(posts);
});
```

---

## PHP 示例

```php
// 获取文章列表
$response = wp_remote_get('http://*************:3080/index.php/wp-json/wp/v2/posts');
$posts = json_decode(wp_remote_retrieve_body($response), true);

// 创建文章
$post_data = array(
    'title' => '新文章标题',
    'content' => '文章内容',
    'status' => 'publish'
);

$response = wp_remote_post('http://*************:3080/index.php/wp-json/wp/v2/posts', array(
    'headers' => array(
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer ' . $token
    ),
    'body' => json_encode($post_data)
));
```

---

## 注意事项

1. **认证**: 读取公开内容无需认证，创建/更新/删除需要适当权限
2. **分页**: 默认每页10条，最大100条
3. **缓存**: 建议实施适当的缓存策略
4. **错误处理**: 始终检查HTTP状态码和响应内容
5. **速率限制**: 避免过于频繁的API调用
6. **CORS**: 跨域请求可能需要特殊配置
