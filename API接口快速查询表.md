# API接口快速查询表

## 基础信息
- **基础URL**: `http://110.42.32.166:3080/index.php/wp-json/`
- **网站**: 测试 (又一个WordPress站点)
- **时区**: Asia/Shanghai (GMT+8)

---

## 🔐 用户认证相关

### 登录认证
```bash
# 主要登录接口
POST /custom-api-cc/login
POST /custom/v5/login

# 凭据验证
POST /custom-api/v1/verify-credentials

# 机器码验证
POST /custom-api-cc/compare_stored_machine_code_endpoint
POST /custom-api-cc/compare_stored_machine_code_endpoint2
```

### JWT令牌管理
```bash
# 查询令牌
GET  /custom-jwt-plugin/v1/query-tk
POST /custom-jwt-plugin/v2/query-token
POST /token_api/v1/token

# 令牌操作
POST /custom-token-plugin/v1/import
POST /myplugin/v1/get-delete-tokens
POST /myplugin/v1/get-time-tokens
POST /myplugin/v1/update-tokens
POST /custom/v1/truncate-token
```

---

## 👤 用户管理

### 用户信息查询
```bash
# 获取用户元数据
GET /user/v1/meta?username={username}

# 获取积分和余额
POST /custom-api-cc/get_points_and_balance
```

### 用户信息修改
```bash
# 修改用户元数据
POST /custom-api/v1/modify-user-meta
POST /custom-api/v1/bulk-modify-user-meta

# 管理员更新用户信息
POST /admin/v1/update-user-meta
# 参数: username, password, target_username, balance, points, product_type

# 重置密码
POST /custom-api/v1/reset-password

# 更新用户余额(拼多多)
POST /custom-api/v1/pdd_update_user_balance
```

### 多重登录管理
```bash
# 添加多重登录
POST /custom-api-cc/add_multi_login
```

---

## 💰 积分余额系统

### 余额操作
```bash
# 增加余额
POST /increase-balance/v1/increase

# 余额返还
POST /fk-balance/v1/fk

# 更新余额
POST /myplugin/v1/update_balance
POST /myplugin/v2/update_balance

# 查询余额
POST /myplugin/v1/query_balance
POST /myplugin/v1/query_balance_log
```

### 积分操作
```bash
# 增加积分
POST /increase-points/v1/increase

# 积分返还
POST /fk-points/v1/fk

# 更新积分
POST /myplugin/v1/update_points

# 查询积分日志
POST /myplugin/v1/query_points_log
```

---

## 📊 数据管理系统

### 数据增删改查
```bash
# 添加数据
POST /custom-jwt-plugin/v1/add-data
POST /custom-jwt-plugin/v1/add-multiple-data

# 查询数据
GET  /custom-jwt-plugin/v1/query-data
POST /custom-jwt-plugin/v1/gzip-query-data
GET  /custom-jwt-plugin/v1/query-data-by-date

# 更新数据
POST /custom-jwt-plugin/v1/manage-data
POST /custom-jwt-plugin/v1/update-multiple-data
POST /custom-jwt-plugin/v1/update-multiple-data2
POST /custom-jwt-plugin/v1/bulk-update-data

# 删除数据
DELETE /custom-jwt-plugin/v1/delete-data
DELETE /custom-jwt-plugin/v1/delete-specific-rows
DELETE /custom-jwt-plugin/v1/delete-specific-rowsyun
POST   /custom-jwt-plugin/v1/delete-multiple-data
```

### 数据库表管理
```bash
# 自定义表操作
POST /custom-jwt-plugin/v1/custom-table
POST /custom-jwt-plugin/v1/custom-table-2
```

---

## 📦 订单管理系统

### 数据上传
```bash
# 上传数据
POST /custom-plugin/v1/upload-data
POST /custom-plugin/v2/upload-data
```

### 订单操作
```bash
# 订单匹配
POST /custom-plugin/v1/match-orders

# 获取订单
POST /custom-plugin/v1/get-distinct-orders
POST /custom-plugin/v2/get-distinct-orders

# 订单摘要
POST /custom/v1/order-summary

# 删除订单
POST /custom-plugin/v1/delete-orders
```

### 数据清理
```bash
# 清空表
POST /custom-plugin/v1/clear-table
POST /custom-plugin/v2/clear-table
```

---

## 🏢 代理管理系统

### 代理信息
```bash
# 获取代理参数
GET /proxy/v1/params?proxy_username={username}

# 获取下级代理
GET /proxy/v1/subordinates?proxy_username={username}
```

---

## ⚙️ 系统配置

### 插件设置
```bash
# 获取设置
GET /my-custom-plugin/v1/get-settings
GET /my-custom-plugin/v2/get-settings

# 自定义消息
GET /myplugin/v1/custom-messages

# 更新插件
POST /myplugin/v1/update
```

### 日期时间
```bash
# 更新日期
POST /myplugin/v1/update_date
POST /myplugin/v2/update_date
```

---

## 🔗 第三方集成

### 拼多多集成
```bash
# 评论转发
POST /pdd-comment-forward/v1/submit
```

### oEmbed嵌入
```bash
# 获取嵌入内容
GET /oembed/1.0/embed?url={url}&format=json&maxwidth=600

# oEmbed代理
GET /oembed/1.0/proxy?url={url}&format=json&maxwidth=600&discover=true
```

---

## 📝 WordPress标准API

### 文章管理
```bash
# 文章列表
GET /wp/v2/posts?per_page=10&page=1&orderby=date&order=desc

# 创建文章
POST /wp/v2/posts

# 获取单篇文章
GET /wp/v2/posts/{id}

# 更新文章
POST /wp/v2/posts/{id}

# 删除文章
DELETE /wp/v2/posts/{id}?force=true
```

### 页面管理
```bash
# 页面列表
GET /wp/v2/pages

# 创建页面
POST /wp/v2/pages

# 获取单个页面
GET /wp/v2/pages/{id}

# 更新页面
POST /wp/v2/pages/{id}

# 删除页面
DELETE /wp/v2/pages/{id}
```

### 分类标签
```bash
# 分类
GET /wp/v2/categories
POST /wp/v2/categories

# 标签
GET /wp/v2/tags
POST /wp/v2/tags

# 自定义分类(topics)
GET /wp/v2/topics
POST /wp/v2/topics
```

---

## 🔄 批量操作

### 批量API
```bash
POST /batch/v1
Content-Type: application/json

{
  "validation": "normal",
  "requests": [
    {
      "method": "POST",
      "path": "/wp/v2/posts",
      "body": {
        "title": "文章1",
        "content": "内容1",
        "status": "publish"
      }
    },
    {
      "method": "POST", 
      "path": "/wp/v2/posts",
      "body": {
        "title": "文章2",
        "content": "内容2",
        "status": "publish"
      }
    }
  ]
}
```

---

## 🚨 错误处理和微信通知

### 错误通知
```bash
# 发送错误消息到微信
POST /custom-api-cc/error_message_to_wechat
```

---

## 📋 常用参数说明

### 通用查询参数
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 10, 最大: 100)
- `search`: 搜索关键词
- `orderby`: 排序字段 (date, title, id等)
- `order`: 排序方向 (asc, desc)
- `context`: 上下文 (view, edit, embed)

### 认证参数
- `username`: 用户名
- `password`: 密码
- `proxy_username`: 代理用户名 (代理接口必需)
- `target_username`: 目标用户名 (管理员接口)

### 业务参数
- `balance`: 余额
- `points`: 积分
- `product_type`: 产品类型

---

## 🔧 开发建议

### 1. 认证流程
```javascript
// 1. 登录获取令牌
const loginResponse = await fetch('/custom-api-cc/login', {
  method: 'POST',
  body: JSON.stringify({ username, password })
});

// 2. 使用令牌进行后续请求
const apiResponse = await fetch('/custom-jwt-plugin/v1/query-data', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 2. 错误处理
```javascript
try {
  const response = await fetch(apiUrl);
  if (!response.ok) {
    // 发送错误到微信
    await fetch('/custom-api-cc/error_message_to_wechat', {
      method: 'POST',
      body: JSON.stringify({ error: response.statusText })
    });
  }
} catch (error) {
  console.error('API调用失败:', error);
}
```

### 3. 批量操作优化
```javascript
// 使用批量API减少请求次数
const batchRequests = data.map(item => ({
  method: 'POST',
  path: '/custom-jwt-plugin/v1/add-data',
  body: item
}));

await fetch('/batch/v1', {
  method: 'POST',
  body: JSON.stringify({
    validation: 'normal',
    requests: batchRequests
  })
});
```

### 4. 数据查询优化
```javascript
// 使用压缩查询减少传输量
const compressedData = await fetch('/custom-jwt-plugin/v1/gzip-query-data', {
  method: 'POST',
  headers: { 'Accept-Encoding': 'gzip' }
});
```

---

## 📞 技术支持

如需技术支持或API使用帮助，请参考：
1. 完整API接口分析文档
2. WordPress官方REST API文档
3. 各插件的具体文档说明

**注意**: 所有自定义接口的具体参数和返回格式需要根据实际插件实现进行调试确认。
