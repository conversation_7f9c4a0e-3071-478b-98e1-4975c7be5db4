# WordPress REST API 接口文档

## 基本信息
- **API版本**: wp/v2
- **基础URL**: `http://110.42.32.166:3080/index.php/wp-json/wp/v2`
- **数据格式**: JSON
- **支持批量操作**: 是 (v1)

---

## 1. API发现接口

### 1.1 获取API信息
- **路径**: `/wp/v2`
- **方法**: `GET`
- **用途**: 获取API的基本信息和可用路由
- **参数**:
  - `namespace`: API命名空间 (默认: wp/v2)
  - `context`: 请求上下文 (默认: view)

---

## 2. 文章管理接口 (Posts)

### 2.1 文章列表
- **路径**: `/wp/v2/posts`
- **方法**: `GET`
- **用途**: 获取文章列表，支持分页、搜索、筛选
- **主要参数**:
  - `page`: 当前页码 (默认: 1)
  - `per_page`: 每页数量 (默认: 10, 最大: 100)
  - `search`: 搜索关键词
  - `author`: 按作者ID筛选
  - `categories`: 按分类筛选
  - `tags`: 按标签筛选
  - `status`: 文章状态 (publish, draft, private等)
  - `order`: 排序方向 (asc, desc)
  - `orderby`: 排序字段 (date, title, author等)
  - `after/before`: 按发布日期筛选
  - `sticky`: 是否置顶文章

### 2.2 创建文章
- **路径**: `/wp/v2/posts`
- **方法**: `POST`
- **用途**: 创建新文章
- **主要参数**:
  - `title`: 文章标题
  - `content`: 文章内容
  - `status`: 发布状态
  - `author`: 作者ID
  - `excerpt`: 摘要
  - `featured_media`: 特色图片ID
  - `categories`: 分类ID数组
  - `tags`: 标签ID数组
  - `meta`: 自定义字段

### 2.3 获取单篇文章
- **路径**: `/wp/v2/posts/{id}`
- **方法**: `GET`
- **用途**: 获取指定ID的文章详情
- **参数**:
  - `id`: 文章ID
  - `context`: 返回字段范围
  - `password`: 密码保护文章的密码

### 2.4 更新文章
- **路径**: `/wp/v2/posts/{id}`
- **方法**: `POST`, `PUT`, `PATCH`
- **用途**: 更新指定文章
- **参数**: 与创建文章相同

### 2.5 删除文章
- **路径**: `/wp/v2/posts/{id}`
- **方法**: `DELETE`
- **用途**: 删除指定文章
- **参数**:
  - `id`: 文章ID
  - `force`: 是否强制删除 (跳过回收站)

---

## 3. 文章修订版本接口

### 3.1 获取文章修订版本列表
- **路径**: `/wp/v2/posts/{parent}/revisions`
- **方法**: `GET`
- **用途**: 获取指定文章的所有修订版本

### 3.2 获取特定修订版本
- **路径**: `/wp/v2/posts/{parent}/revisions/{id}`
- **方法**: `GET`
- **用途**: 获取指定修订版本的详情

### 3.3 删除修订版本
- **路径**: `/wp/v2/posts/{parent}/revisions/{id}`
- **方法**: `DELETE`
- **用途**: 删除指定修订版本

---

## 4. 文章自动保存接口

### 4.1 获取自动保存列表
- **路径**: `/wp/v2/posts/{id}/autosaves`
- **方法**: `GET`
- **用途**: 获取文章的自动保存版本

### 4.2 创建自动保存
- **路径**: `/wp/v2/posts/{id}/autosaves`
- **方法**: `POST`
- **用途**: 为文章创建自动保存版本

### 4.3 获取特定自动保存
- **路径**: `/wp/v2/posts/{parent}/autosaves/{id}`
- **方法**: `GET`
- **用途**: 获取指定自动保存版本

---

## 5. 页面管理接口 (Pages)

### 5.1 页面列表
- **路径**: `/wp/v2/pages`
- **方法**: `GET`
- **用途**: 获取页面列表
- **特有参数**:
  - `parent`: 父页面ID
  - `parent_exclude`: 排除的父页面ID
  - `menu_order`: 菜单排序

### 5.2 创建页面
- **路径**: `/wp/v2/pages`
- **方法**: `POST`
- **用途**: 创建新页面
- **特有参数**:
  - `parent`: 父页面ID
  - `menu_order`: 菜单排序

### 5.3 页面的其他操作
- 获取、更新、删除页面的接口与文章类似
- 页面同样支持修订版本和自动保存功能

---

## 6. 常用查询参数说明

### 6.1 分页参数
- `page`: 页码 (从1开始)
- `per_page`: 每页数量 (1-100)
- `offset`: 跳过的记录数

### 6.2 搜索参数
- `search`: 搜索关键词
- `search_columns`: 搜索的字段 (post_title, post_content, post_excerpt)
- `search_semantics`: 搜索语义 (exact)

### 6.3 筛选参数
- `include`: 包含的ID列表
- `exclude`: 排除的ID列表
- `slug`: 按别名筛选
- `status`: 按状态筛选

### 6.4 日期参数
- `after`: 指定日期之后
- `before`: 指定日期之前
- `modified_after`: 修改日期之后
- `modified_before`: 修改日期之前

### 6.5 分类法参数
- `categories`: 分类筛选 (支持高级查询)
- `categories_exclude`: 排除分类
- `tags`: 标签筛选
- `tags_exclude`: 排除标签
- `topics`: 主题筛选 (自定义分类法)
- `tax_relation`: 分类法关系 (AND/OR)

---

## 7. 响应格式

### 7.1 成功响应
```json
{
  "id": 1,
  "title": {"rendered": "文章标题"},
  "content": {"rendered": "文章内容"},
  "status": "publish",
  "author": 1,
  "date": "2024-01-01T00:00:00",
  "_links": {...}
}
```

### 7.2 列表响应
- 返回数组格式
- 包含分页信息在HTTP头中
- `X-WP-Total`: 总记录数
- `X-WP-TotalPages`: 总页数

---

## 8. 认证说明
- 读取公开内容无需认证
- 创建、更新、删除需要适当权限
- 支持多种认证方式 (Cookie, OAuth, Application Password等)

---

## 9. 错误处理
- HTTP状态码指示操作结果
- 错误响应包含详细错误信息
- 常见状态码: 200(成功), 400(请求错误), 401(未授权), 404(未找到)

---

## 10. 使用示例

### 获取最新10篇文章
```
GET /wp/v2/posts?per_page=10&orderby=date&order=desc
```

### 搜索包含"WordPress"的文章
```
GET /wp/v2/posts?search=WordPress
```

### 获取特定分类的文章
```
GET /wp/v2/posts?categories=5
```

### 创建新文章
```
POST /wp/v2/posts
Content-Type: application/json

{
  "title": "新文章标题",
  "content": "文章内容",
  "status": "publish"
}
```

---

## 11. 其他重要接口类型

### 11.1 媒体文件接口
- **路径**: `/wp/v2/media`
- **用途**: 管理图片、视频等媒体文件
- **支持操作**: 上传、获取、更新、删除媒体文件

### 11.2 用户接口
- **路径**: `/wp/v2/users`
- **用途**: 管理用户信息
- **支持操作**: 获取用户列表、用户详情、创建用户

### 11.3 评论接口
- **路径**: `/wp/v2/comments`
- **用途**: 管理文章评论
- **支持操作**: 获取评论、创建评论、审核评论

### 11.4 分类接口
- **路径**: `/wp/v2/categories`
- **用途**: 管理文章分类
- **支持操作**: 获取分类列表、创建分类、更新分类

### 11.5 标签接口
- **路径**: `/wp/v2/tags`
- **用途**: 管理文章标签
- **支持操作**: 获取标签列表、创建标签、更新标签

### 11.6 自定义分类法接口
- **路径**: `/wp/v2/topics` (示例中的自定义分类法)
- **用途**: 管理自定义分类法术语
- **支持操作**: 与标准分类、标签类似

### 11.7 设置接口
- **路径**: `/wp/v2/settings`
- **用途**: 获取和更新网站设置
- **需要**: 管理员权限

### 11.8 主题接口
- **路径**: `/wp/v2/themes`
- **用途**: 管理WordPress主题
- **需要**: 管理员权限

### 11.9 插件接口
- **路径**: `/wp/v2/plugins`
- **用途**: 管理WordPress插件
- **需要**: 管理员权限

---

## 12. 高级功能

### 12.1 批量操作
- 支持在单个请求中处理多个操作
- 使用 `allow_batch` 参数
- 提高API调用效率

### 12.2 嵌入关联数据
- 使用 `_embed` 参数
- 自动包含相关联的数据 (作者、分类、标签等)
- 减少API调用次数

### 12.3 字段选择
- 使用 `_fields` 参数
- 只返回需要的字段
- 减少数据传输量

### 12.4 自定义字段
- 通过 `meta` 参数管理
- 支持各种数据类型
- 需要适当权限

---

## 13. 安全注意事项

### 13.1 权限控制
- 不同操作需要不同权限级别
- 读取公开内容: 无需权限
- 创建内容: 需要发布权限
- 管理设置: 需要管理员权限

### 13.2 数据验证
- API会验证输入数据
- 防止恶意数据注入
- 遵循WordPress安全标准

### 13.3 速率限制
- 可能存在API调用频率限制
- 建议合理控制请求频率
- 使用缓存减少不必要的请求

---

## 14. 开发建议

### 14.1 错误处理
- 始终检查HTTP状态码
- 处理网络错误和超时
- 提供用户友好的错误信息

### 14.2 性能优化
- 使用适当的分页大小
- 利用字段选择减少数据量
- 实施客户端缓存策略

### 14.3 版本兼容性
- 当前使用 wp/v2 版本
- 注意API版本更新
- 测试新版本兼容性

---

## 15. 总结

这个WordPress REST API提供了完整的内容管理功能，包括：

1. **内容管理**: 文章、页面的完整CRUD操作
2. **版本控制**: 修订版本和自动保存功能
3. **分类管理**: 分类、标签、自定义分类法
4. **用户管理**: 用户信息和权限控制
5. **媒体管理**: 文件上传和管理
6. **系统管理**: 设置、主题、插件管理

该API设计遵循REST原则，提供了灵活的查询和筛选功能，支持现代Web应用的各种需求。开发者可以基于这些接口构建各种类型的应用，从简单的博客到复杂的内容管理系统。
