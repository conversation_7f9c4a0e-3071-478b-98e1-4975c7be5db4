# WordPress REST API - 用户接口详细说明

## 基本信息
- **基础路径**: `/wp/v2/users`
- **基础URL**: `http://*************:3080/index.php/wp-json/wp/v2/users`
- **支持格式**: JSON
- **认证要求**: 大部分操作需要适当权限

---

## 1. 用户接口概览

| 操作 | HTTP方法 | 路径 | 权限要求 | 说明 |
|------|----------|------|----------|------|
| 获取用户列表 | GET | `/wp/v2/users` | 无 (公开信息) | 获取所有用户的基本信息 |
| 获取单个用户 | GET | `/wp/v2/users/{id}` | 无 (公开信息) | 获取指定用户的详细信息 |
| 创建用户 | POST | `/wp/v2/users` | 管理员权限 | 创建新用户账户 |
| 更新用户 | POST/PUT/PATCH | `/wp/v2/users/{id}` | 用户本人或管理员 | 更新用户信息 |
| 删除用户 | DELETE | `/wp/v2/users/{id}` | 管理员权限 | 删除用户账户 |
| 获取当前用户 | GET | `/wp/v2/users/me` | 已登录用户 | 获取当前登录用户信息 |

---

## 2. 用户数据结构

### 2.1 完整用户对象
```json
{
  "id": 1,
  "username": "admin",
  "name": "管理员",
  "first_name": "张",
  "last_name": "三",
  "email": "<EMAIL>",
  "url": "https://example.com",
  "description": "网站管理员",
  "link": "http://*************:3080/author/admin/",
  "locale": "zh_CN",
  "nickname": "admin",
  "slug": "admin",
  "registered_date": "2024-01-01T00:00:00",
  "roles": ["administrator"],
  "capabilities": {
    "switch_themes": true,
    "edit_themes": true,
    "activate_plugins": true,
    "edit_plugins": true,
    "edit_users": true,
    "edit_files": true,
    "manage_options": true,
    "moderate_comments": true,
    "manage_categories": true,
    "manage_links": true,
    "upload_files": true,
    "import": true,
    "unfiltered_html": true,
    "edit_posts": true,
    "edit_others_posts": true,
    "edit_published_posts": true,
    "publish_posts": true,
    "edit_pages": true,
    "read": true,
    "level_10": true,
    "level_9": true,
    "level_8": true,
    "level_7": true,
    "level_6": true,
    "level_5": true,
    "level_4": true,
    "level_3": true,
    "level_2": true,
    "level_1": true,
    "level_0": true,
    "edit_others_pages": true,
    "edit_published_pages": true,
    "publish_pages": true,
    "delete_pages": true,
    "delete_others_pages": true,
    "delete_published_pages": true,
    "delete_posts": true,
    "delete_others_posts": true,
    "delete_published_posts": true,
    "delete_private_posts": true,
    "edit_private_posts": true,
    "read_private_posts": true,
    "delete_private_pages": true,
    "edit_private_pages": true,
    "read_private_pages": true,
    "delete_users": true,
    "create_users": true,
    "unfiltered_upload": true,
    "edit_dashboard": true,
    "update_plugins": true,
    "delete_plugins": true,
    "install_plugins": true,
    "update_themes": true,
    "install_themes": true,
    "update_core": true,
    "list_users": true,
    "remove_users": true,
    "promote_users": true,
    "edit_theme_options": true,
    "delete_themes": true,
    "export": true
  },
  "extra_capabilities": {
    "administrator": true
  },
  "avatar_urls": {
    "24": "http://2.gravatar.com/avatar/hash?s=24&d=mm&r=g",
    "48": "http://2.gravatar.com/avatar/hash?s=48&d=mm&r=g",
    "96": "http://2.gravatar.com/avatar/hash?s=96&d=mm&r=g"
  },
  "meta": {},
  "_links": {
    "self": [
      {
        "href": "http://*************:3080/index.php/wp-json/wp/v2/users/1"
      }
    ],
    "collection": [
      {
        "href": "http://*************:3080/index.php/wp-json/wp/v2/users"
      }
    ]
  }
}
```

### 2.2 公开用户信息 (未认证访问)
```json
{
  "id": 1,
  "name": "管理员",
  "url": "https://example.com",
  "description": "网站管理员",
  "link": "http://*************:3080/author/admin/",
  "slug": "admin",
  "avatar_urls": {
    "24": "http://2.gravatar.com/avatar/hash?s=24&d=mm&r=g",
    "48": "http://2.gravatar.com/avatar/hash?s=48&d=mm&r=g",
    "96": "http://2.gravatar.com/avatar/hash?s=96&d=mm&r=g"
  }
}
```

---

## 3. 获取用户信息

### 3.1 获取用户列表
```bash
# 基本请求
curl "http://*************:3080/index.php/wp-json/wp/v2/users"

# 带参数的请求
curl "http://*************:3080/index.php/wp-json/wp/v2/users?per_page=5&orderby=registered_date&order=desc"
```

**常用查询参数**:
- `context`: 返回数据的上下文 (`view`, `embed`, `edit`)
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 10, 最大: 100)
- `search`: 搜索用户名、邮箱、显示名称
- `exclude`: 排除的用户ID数组
- `include`: 包含的用户ID数组
- `offset`: 跳过的用户数量
- `order`: 排序方向 (`asc`, `desc`)
- `orderby`: 排序字段 (`id`, `include`, `name`, `registered_date`, `slug`, `include_slugs`, `email`, `url`)
- `slug`: 按用户别名筛选
- `roles`: 按用户角色筛选
- `capabilities`: 按用户权限筛选
- `who`: 特殊筛选 (`authors` - 只返回有发布权限的用户)

### 3.2 获取单个用户
```bash
# 通过ID获取
curl "http://*************:3080/index.php/wp-json/wp/v2/users/1"

# 获取当前登录用户
curl "http://*************:3080/index.php/wp-json/wp/v2/users/me" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取编辑上下文的完整信息 (需要权限)
curl "http://*************:3080/index.php/wp-json/wp/v2/users/1?context=edit" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 4. 创建用户

### 4.1 创建用户的基本要求
- **权限**: 需要管理员权限或 `create_users` 权限
- **必填字段**: `username`, `email`, `password`
- **可选字段**: `name`, `first_name`, `last_name`, `nickname`, `slug`, `url`, `description`, `locale`, `roles`, `meta`

### 4.2 创建用户示例

#### 基本创建
```bash
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "name": "新用户",
    "first_name": "新",
    "last_name": "用户",
    "roles": ["subscriber"]
  }'
```

#### 完整创建示例
```bash
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "username": "editor_user",
    "email": "<EMAIL>",
    "password": "StrongPassword456!",
    "name": "编辑用户",
    "first_name": "编辑",
    "last_name": "用户",
    "nickname": "编辑小助手",
    "slug": "editor-user",
    "url": "https://editor.example.com",
    "description": "负责内容编辑的用户",
    "locale": "zh_CN",
    "roles": ["editor"],
    "meta": {
      "custom_field": "自定义值"
    }
  }'
```

### 4.3 JavaScript 创建用户示例
```javascript
async function createUser(userData, adminToken) {
  try {
    const response = await fetch('http://*************:3080/index.php/wp-json/wp/v2/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify({
        username: userData.username,
        email: userData.email,
        password: userData.password,
        name: userData.name,
        first_name: userData.firstName,
        last_name: userData.lastName,
        roles: userData.roles || ['subscriber']
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const newUser = await response.json();
    console.log('用户创建成功:', newUser);
    return newUser;
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
}

// 使用示例
const newUserData = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: '测试用户',
  firstName: '测试',
  lastName: '用户',
  roles: ['contributor']
};

createUser(newUserData, 'your_admin_token_here');
```

### 4.4 PHP 创建用户示例
```php
function create_wordpress_user($user_data, $admin_token) {
    $url = 'http://*************:3080/index.php/wp-json/wp/v2/users';
    
    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $admin_token
        ),
        'body' => json_encode($user_data)
    );
    
    $response = wp_remote_request($url, $args);
    
    if (is_wp_error($response)) {
        return array('error' => $response->get_error_message());
    }
    
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    
    if ($status_code === 201) {
        return json_decode($body, true);
    } else {
        return array('error' => '创建用户失败', 'status' => $status_code, 'response' => $body);
    }
}

// 使用示例
$new_user = array(
    'username' => 'phpuser',
    'email' => '<EMAIL>',
    'password' => 'PhpPassword123!',
    'name' => 'PHP用户',
    'first_name' => 'PHP',
    'last_name' => '用户',
    'roles' => array('author')
);

$result = create_wordpress_user($new_user, 'your_admin_token');
if (isset($result['error'])) {
    echo '错误: ' . $result['error'];
} else {
    echo '用户创建成功，ID: ' . $result['id'];
}
```

---

## 5. 更新用户信息

### 5.1 更新用户的权限要求
- **用户本人**: 可以更新自己的基本信息 (姓名、邮箱、密码、个人资料等)
- **管理员**: 可以更新任何用户的所有信息，包括角色和权限
- **不能更新的字段**: `id`, `registered_date`, `capabilities` (通过角色自动计算)

### 5.2 更新用户基本信息
```bash
# 更新用户基本信息
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/users/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer USER_OR_ADMIN_TOKEN" \
  -d '{
    "name": "更新后的姓名",
    "first_name": "更新",
    "last_name": "姓名",
    "email": "<EMAIL>",
    "url": "https://newwebsite.com",
    "description": "更新后的个人简介"
  }'

# 更新密码
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/users/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer USER_OR_ADMIN_TOKEN" \
  -d '{
    "password": "NewSecurePassword789!"
  }'

# 管理员更新用户角色
curl -X POST "http://*************:3080/index.php/wp-json/wp/v2/users/123" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -d '{
    "roles": ["editor"]
  }'
```

### 5.3 JavaScript 更新用户示例
```javascript
async function updateUser(userId, updateData, token) {
  try {
    const response = await fetch(`http://*************:3080/index.php/wp-json/wp/v2/users/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const updatedUser = await response.json();
    console.log('用户更新成功:', updatedUser);
    return updatedUser;
  } catch (error) {
    console.error('更新用户失败:', error);
    throw error;
  }
}

// 更新基本信息示例
const updateData = {
  name: '新的显示名称',
  email: '<EMAIL>',
  description: '更新后的个人简介'
};

updateUser(123, updateData, 'user_token_here');

// 管理员更新用户角色示例
const roleUpdateData = {
  roles: ['editor']
};

updateUser(123, roleUpdateData, 'admin_token_here');
```

### 5.4 PHP 更新用户示例
```php
function update_wordpress_user($user_id, $update_data, $token) {
    $url = "http://*************:3080/index.php/wp-json/wp/v2/users/{$user_id}";

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token
        ),
        'body' => json_encode($update_data)
    );

    $response = wp_remote_request($url, $args);

    if (is_wp_error($response)) {
        return array('error' => $response->get_error_message());
    }

    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);

    if ($status_code === 200) {
        return json_decode($body, true);
    } else {
        return array('error' => '更新用户失败', 'status' => $status_code, 'response' => $body);
    }
}

// 使用示例
$update_data = array(
    'name' => '更新后的姓名',
    'email' => '<EMAIL>',
    'description' => '更新后的个人简介'
);

$result = update_wordpress_user(123, $update_data, 'user_token');
if (isset($result['error'])) {
    echo '错误: ' . $result['error'];
} else {
    echo '用户更新成功';
}
```

---

## 6. 删除用户

### 6.1 删除用户权限
- **权限要求**: 管理员权限或 `delete_users` 权限
- **注意**: 删除用户时需要处理该用户的文章归属

### 6.2 删除用户示例
```bash
# 删除用户 (移动文章到其他用户)
curl -X DELETE "http://*************:3080/index.php/wp-json/wp/v2/users/123?reassign=1&force=true" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 删除用户 (删除用户的所有文章)
curl -X DELETE "http://*************:3080/index.php/wp-json/wp/v2/users/123?force=true" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

**删除参数**:
- `force`: 是否强制删除 (必须为 true)
- `reassign`: 将被删除用户的文章重新分配给指定用户ID

---

## 7. 用户角色说明

### 7.1 WordPress 默认角色
| 角色 | 英文名 | 主要权限 |
|------|--------|----------|
| 超级管理员 | `super_admin` | 多站点网络的最高权限 |
| 管理员 | `administrator` | 网站的完全控制权限 |
| 编辑 | `editor` | 管理所有文章和页面 |
| 作者 | `author` | 发布和管理自己的文章 |
| 贡献者 | `contributor` | 编写和编辑自己的文章，但不能发布 |
| 订阅者 | `subscriber` | 只能阅读内容和管理个人资料 |

### 7.2 角色权限对照
```json
{
  "administrator": [
    "switch_themes", "edit_themes", "activate_plugins", "edit_plugins",
    "edit_users", "edit_files", "manage_options", "moderate_comments",
    "manage_categories", "manage_links", "upload_files", "import",
    "unfiltered_html", "edit_posts", "edit_others_posts", "edit_published_posts",
    "publish_posts", "edit_pages", "read", "edit_others_pages",
    "edit_published_pages", "publish_pages", "delete_pages",
    "delete_others_pages", "delete_published_pages", "delete_posts",
    "delete_others_posts", "delete_published_posts", "delete_private_posts",
    "edit_private_posts", "read_private_posts", "delete_private_pages",
    "edit_private_pages", "read_private_pages", "delete_users",
    "create_users", "unfiltered_upload", "edit_dashboard",
    "update_plugins", "delete_plugins", "install_plugins",
    "update_themes", "install_themes", "update_core",
    "list_users", "remove_users", "promote_users",
    "edit_theme_options", "delete_themes", "export"
  ],
  "editor": [
    "moderate_comments", "manage_categories", "manage_links",
    "upload_files", "unfiltered_html", "edit_posts", "edit_others_posts",
    "edit_published_posts", "publish_posts", "edit_pages", "read",
    "edit_others_pages", "edit_published_pages", "publish_pages",
    "delete_pages", "delete_others_pages", "delete_published_pages",
    "delete_posts", "delete_others_posts", "delete_published_posts",
    "delete_private_posts", "edit_private_posts", "read_private_posts",
    "delete_private_pages", "edit_private_pages", "read_private_pages"
  ],
  "author": [
    "upload_files", "edit_posts", "edit_published_posts", "publish_posts",
    "read", "delete_posts", "delete_published_posts"
  ],
  "contributor": [
    "edit_posts", "read", "delete_posts"
  ],
  "subscriber": [
    "read"
  ]
}
```

---

## 8. 常见错误和解决方案

### 8.1 常见HTTP状态码
- `200`: 操作成功
- `201`: 用户创建成功
- `400`: 请求参数错误
- `401`: 未授权 (需要登录)
- `403`: 禁止访问 (权限不足)
- `404`: 用户不存在
- `409`: 冲突 (用户名或邮箱已存在)
- `500`: 服务器内部错误

### 8.2 常见错误示例
```json
{
  "code": "existing_user_login",
  "message": "抱歉，该用户名已存在！",
  "data": {
    "status": 400
  }
}

{
  "code": "existing_user_email",
  "message": "抱歉，该电子邮箱地址已经存在！",
  "data": {
    "status": 400
  }
}

{
  "code": "rest_user_cannot_create",
  "message": "抱歉，您不被允许创建新用户。",
  "data": {
    "status": 403
  }
}
```

---

## 9. 最佳实践

### 9.1 安全建议
1. **强密码**: 确保用户密码符合安全要求
2. **权限最小化**: 只给用户必要的权限
3. **输入验证**: 验证所有用户输入
4. **HTTPS**: 在生产环境中使用HTTPS
5. **令牌安全**: 妥善保管认证令牌

### 9.2 性能优化
1. **分页**: 获取用户列表时使用适当的分页
2. **字段选择**: 只获取需要的字段
3. **缓存**: 实施适当的缓存策略
4. **批量操作**: 避免频繁的单个用户操作

### 9.3 错误处理
```javascript
async function safeUserOperation(operation) {
  try {
    const result = await operation();
    return { success: true, data: result };
  } catch (error) {
    console.error('用户操作失败:', error);

    if (error.response) {
      // API返回的错误
      const errorData = await error.response.json();
      return {
        success: false,
        error: errorData.message || '操作失败',
        code: errorData.code
      };
    } else {
      // 网络或其他错误
      return {
        success: false,
        error: '网络错误或服务不可用'
      };
    }
  }
}
```

这个详细的用户接口文档涵盖了WordPress REST API中用户管理的所有重要方面，包括创建、更新、删除用户的完整流程和实际代码示例。
