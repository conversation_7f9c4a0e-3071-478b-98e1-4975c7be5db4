# 完整API接口分析文档

## 网站基本信息
- **网站名称**: 测试
- **描述**: 又一个WordPress站点
- **基础URL**: `http://110.42.32.166:3080`
- **API基础路径**: `http://110.42.32.166:3080/index.php/wp-json/`
- **时区**: Asia/Shanghai (GMT+8)

---

## API命名空间概览

该WordPress网站包含以下API命名空间：

| 命名空间 | 版本 | 类型 | 主要功能 |
|----------|------|------|----------|
| `oembed/1.0` | v1.0 | 标准 | oEmbed嵌入内容 |
| `wp/v2` | v2 | 标准 | WordPress核心API |
| `wp-site-health/v1` | v1 | 标准 | 网站健康检查 |
| `wp-block-editor/v1` | v1 | 标准 | 区块编辑器 |
| `custom-api/v1` | v1 | 自定义 | 自定义API功能 |
| `custom-jwt-plugin/v1` | v1 | 自定义 | JWT认证和数据管理 |
| `custom-jwt-plugin/v2` | v2 | 自定义 | JWT认证升级版 |
| `custom/v1` | v1 | 自定义 | 订单摘要功能 |
| `custom/v5` | v5 | 自定义 | 登录功能 |
| `increase-balance/v1` | v1 | 自定义 | 余额增加 |
| `increase-points/v1` | v1 | 自定义 | 积分增加 |
| `fk-balance/v1` | v1 | 自定义 | 余额返还 |
| `fk-points/v1` | v1 | 自定义 | 积分返还 |
| `myplugin/v1` | v1 | 自定义 | 插件功能集合 |
| `myplugin/v2` | v2 | 自定义 | 插件功能升级版 |
| `my-custom-plugin/v1` | v1 | 自定义 | 设置管理 |
| `my-custom-plugin/v2` | v2 | 自定义 | 设置管理升级版 |
| `custom-api-cc` | - | 自定义 | 用户认证和机器码验证 |
| `custom-token-plugin/v1` | v1 | 自定义 | 令牌导入 |
| `token_api/v1` | v1 | 自定义 | 令牌API |
| `custom-plugin/v1` | v1 | 自定义 | 数据上传和订单管理 |
| `custom-plugin/v2` | v2 | 自定义 | 数据上传和订单管理升级版 |
| `proxy/v1` | v1 | 自定义 | 代理参数和下级管理 |
| `user/v1` | v1 | 自定义 | 用户元数据 |
| `admin/v1` | v1 | 自定义 | 管理员功能 |
| `pdd-comment-forward/v1` | v1 | 自定义 | 拼多多评论转发 |

---

## 1. 标准WordPress API (wp/v2)

### 1.1 核心内容管理
- **文章管理**: `/wp/v2/posts` - 完整的CRUD操作
- **页面管理**: `/wp/v2/pages` - 页面的完整管理
- **修订版本**: `/wp/v2/posts/{id}/revisions` - 版本控制
- **自动保存**: `/wp/v2/posts/{id}/autosaves` - 自动保存功能

### 1.2 分类和标签
- **分类管理**: `/wp/v2/categories`
- **标签管理**: `/wp/v2/tags`
- **自定义分类**: `/wp/v2/topics` (自定义分类法)

---

## 2. 用户认证和安全系统

### 2.1 JWT认证插件 (custom-jwt-plugin/v1 & v2)

#### 数据管理接口
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-jwt-plugin/v1/add-data` | POST | 添加单条数据 |
| `/custom-jwt-plugin/v1/add-multiple-data` | POST | 批量添加数据 |
| `/custom-jwt-plugin/v1/manage-data` | POST | 管理数据 |
| `/custom-jwt-plugin/v1/query-data` | GET | 查询数据 |
| `/custom-jwt-plugin/v1/gzip-query-data` | POST | 压缩查询数据 |
| `/custom-jwt-plugin/v1/query-tk` | GET | 查询令牌 |
| `/custom-jwt-plugin/v1/query-data-by-date` | GET | 按日期查询数据 |
| `/custom-jwt-plugin/v1/update-multiple-data` | POST | 批量更新数据 |
| `/custom-jwt-plugin/v1/update-multiple-data2` | POST | 批量更新数据(版本2) |
| `/custom-jwt-plugin/v1/bulk-update-data` | POST | 大批量更新数据 |
| `/custom-jwt-plugin/v1/delete-data` | DELETE | 删除数据 |
| `/custom-jwt-plugin/v1/delete-specific-rows` | DELETE | 删除特定行 |
| `/custom-jwt-plugin/v1/delete-specific-rowsyun` | DELETE | 删除特定行(云版本) |
| `/custom-jwt-plugin/v1/delete-multiple-data` | POST | 批量删除数据 |

#### 数据库表管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-jwt-plugin/v1/custom-table` | POST | 自定义表操作 |
| `/custom-jwt-plugin/v1/custom-table-2` | POST | 自定义表操作(版本2) |

#### v2版本接口
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-jwt-plugin/v2/query-token` | POST | 查询令牌(v2) |

### 2.2 用户认证系统 (custom-api-cc)

| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-api-cc/login` | POST | 用户登录 |
| `/custom-api-cc/compare_stored_machine_code_endpoint` | POST | 机器码验证 |
| `/custom-api-cc/compare_stored_machine_code_endpoint2` | POST | 机器码验证(版本2) |
| `/custom-api-cc/add_multi_login` | POST | 添加多重登录 |
| `/custom-api-cc/get_points_and_balance` | POST | 获取积分和余额 |
| `/custom-api-cc/error_message_to_wechat` | POST | 错误消息发送到微信 |

### 2.3 其他登录接口
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom/v5/login` | POST | 自定义登录(v5) |

---

## 3. 积分和余额管理系统

### 3.1 余额管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/increase-balance/v1/increase` | POST | 增加余额 |
| `/fk-balance/v1/fk` | POST | 余额返还 |
| `/myplugin/v1/update_balance` | POST | 更新余额(v1) |
| `/myplugin/v2/update_balance` | POST | 更新余额(v2) |
| `/myplugin/v1/query_balance` | POST | 查询余额 |
| `/myplugin/v1/query_balance_log` | POST | 查询余额日志 |

### 3.2 积分管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/increase-points/v1/increase` | POST | 增加积分 |
| `/fk-points/v1/fk` | POST | 积分返还 |
| `/myplugin/v1/update_points` | POST | 更新积分 |
| `/myplugin/v1/query_points_log` | POST | 查询积分日志 |

---

## 4. 用户管理系统

### 4.1 用户元数据管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/user/v1/meta` | GET | 获取用户元数据 |
| `/custom-api/v1/modify-user-meta` | POST | 修改用户元数据 |
| `/custom-api/v1/bulk-modify-user-meta` | POST | 批量修改用户元数据 |
| `/custom-api/v1/pdd_update_user_balance` | POST | 更新用户余额(拼多多) |
| `/custom-api/v1/reset-password` | POST | 重置密码 |

### 4.2 管理员功能
| 接口路径 | 方法 | 功能描述 | 参数 |
|----------|------|----------|------|
| `/admin/v1/update-user-meta` | POST | 管理员更新用户元数据 | username, password, target_username, balance, points, product_type |

### 4.3 身份验证
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-api/v1/verify-credentials` | POST | 验证凭据 |

---

## 5. 数据管理和上传系统

### 5.1 数据上传 (custom-plugin/v1 & v2)
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-plugin/v1/upload-data` | POST | 上传数据(v1) |
| `/custom-plugin/v2/upload-data` | POST | 上传数据(v2) |

### 5.2 订单管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-plugin/v1/match-orders` | POST | 匹配订单 |
| `/custom-plugin/v1/get-distinct-orders` | POST | 获取不重复订单(v1) |
| `/custom-plugin/v2/get-distinct-orders` | POST | 获取不重复订单(v2) |
| `/custom-plugin/v1/delete-orders` | POST | 删除订单 |
| `/custom/v1/order-summary` | POST | 订单摘要 |

### 5.3 数据清理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/custom-plugin/v1/clear-table` | POST | 清空表(v1) |
| `/custom-plugin/v2/clear-table` | POST | 清空表(v2) |

---

## 6. 令牌管理系统

### 6.1 令牌操作
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/token_api/v1/token` | POST | 令牌API |
| `/custom-token-plugin/v1/import` | POST | 导入令牌 |
| `/myplugin/v1/get-delete-tokens` | POST | 获取删除令牌 |
| `/myplugin/v1/get-time-tokens` | POST | 获取时间令牌 |
| `/myplugin/v1/update-tokens` | POST | 更新令牌 |
| `/custom/v1/truncate-token` | POST | 截断令牌 |

---

## 7. 系统管理和配置

### 7.1 插件设置
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/my-custom-plugin/v1/get-settings` | GET | 获取设置(v1) |
| `/my-custom-plugin/v2/get-settings` | GET | 获取设置(v2) |
| `/myplugin/v1/custom-messages` | GET | 自定义消息 |
| `/myplugin/v1/update` | POST | 更新插件 |

### 7.2 日期和时间管理
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/myplugin/v1/update_date` | POST | 更新日期(v1) |
| `/myplugin/v2/update_date` | POST | 更新日期(v2) |

---

## 8. 代理和层级管理

### 8.1 代理系统 (proxy/v1)
| 接口路径 | 方法 | 功能描述 | 参数 |
|----------|------|----------|------|
| `/proxy/v1/params` | GET | 获取代理参数 | proxy_username (必需) |
| `/proxy/v1/subordinates` | GET | 获取下级代理 | proxy_username (必需) |

---

## 9. 第三方集成

### 9.1 拼多多集成
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/pdd-comment-forward/v1/submit` | POST | 拼多多评论转发 |

### 9.2 oEmbed支持
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/oembed/1.0/embed` | GET | 获取嵌入内容 |
| `/oembed/1.0/proxy` | GET | oEmbed代理 |

---

## 10. 批量操作支持

### 10.1 批量API
| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/batch/v1` | POST | 批量操作接口 |

**批量操作参数**:
- `validation`: 验证模式 (require-all-validate, normal)
- `requests`: 请求数组 (最大25个)
- 支持的方法: POST, PUT, PATCH, DELETE

---

## 使用建议

### 认证要求
1. **公开接口**: oEmbed、部分查询接口无需认证
2. **用户接口**: 需要用户登录凭据
3. **管理接口**: 需要管理员权限
4. **自定义接口**: 可能需要特定的API密钥或JWT令牌

### 安全注意事项
1. 所有涉及用户数据的操作都需要适当的权限验证
2. 机器码验证接口用于设备绑定
3. 余额和积分操作需要严格的权限控制
4. 建议在生产环境中使用HTTPS

### 性能优化
1. 使用批量操作接口减少API调用次数
2. 合理使用分页参数
3. 对频繁查询的数据实施缓存策略
4. 监控API调用频率避免超出限制

这个WordPress网站显然是一个功能非常丰富的系统，集成了用户管理、积分系统、订单处理、代理管理等多种业务功能。
